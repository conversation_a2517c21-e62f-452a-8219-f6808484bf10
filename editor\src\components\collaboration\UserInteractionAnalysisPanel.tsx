/**
 * 用户交互分析面板组件
 * 用于可视化用户交互数据
 */
import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Tabs,
  Table,
  Select,
  Button,
  Space,
  Tag,
  Typography,
  Input,
  Empty,
  Divider,
  Popconfirm,
  Modal,
  Statistic,
  Row,
  Col} from 'antd';
import {
  UserOutlined,
  HistoryOutlined,
  DeleteOutlined,
  SearchOutlined,
  FilterOutlined,
  ReloadOutlined,
  Bar<PERSON><PERSON>Outlined,
  Line<PERSON><PERSON>Outlined,
  Pie<PERSON>hartOutlined,
  HeatMapOutlined,
  AreaChartOutlined,
  SettingOutlined,
  DownloadOutlined,
  EyeOutlined,
  ClockCircleOutlined,
  TeamOutlined,
  ToolOutlined,
  FileOutlined,
  DashboardOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import { selectUsers } from '../../store/collaboration/collaborationSlice';
import { collaborationService } from '../../services/CollaborationService';
import { userInteractionLogService, InteractionLog, InteractionLogType, InteractionLogLevel } from '../../services/UserInteractionLogService';
import { userBehaviorAnalyzer, UserAction, UserActionType } from '../../components/testing/UserBehaviorAnalyzer';
import ReactECharts from 'echarts-for-react';

const { Text, Title } = Typography;
const { Option } = Select;
import { Select, Switch, InputNumber, Form } from 'antd';
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;
const { confirm } = Modal;

interface UserInteractionAnalysisPanelProps {
  className?: string;
}

/**
 * 用户交互分析面板组件
 */
const UserInteractionAnalysisPanel: React.FC<UserInteractionAnalysisPanelProps> = ({ className }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  
  // 从Redux获取数据
  const users = useSelector(selectUsers);
  const currentUserId = collaborationService.getUserId();
  
  // 本地状态
  const [logs, setLogs] = useState<InteractionLog[]>([]);
  const [selectedUserId, setSelectedUserId] = useState<string>('');
  const [searchText, setSearchText] = useState<string>('');
  const [logTypeFilter, setLogTypeFilter] = useState<InteractionLogType[]>([]);
  const [logLevelFilter, setLogLevelFilter] = useState<InteractionLogLevel[]>([]);
  const [dateRange, setDateRange] = useState<[number, number] | null>(null);
  const [filteredLogs, setFilteredLogs] = useState<InteractionLog[]>([]);
  const [detailModalVisible, setDetailModalVisible] = useState<boolean>(false);
  const [selectedLog, setSelectedLog] = useState<InteractionLog | null>(null);
  const [loggingEnabled, setLoggingEnabled] = useState<boolean>(true);
  const [activeTab, setActiveTab] = useState<string>('overview');
  const [chartType, setChartType] = useState<string>('bar');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [analyticsData, setAnalyticsData] = useState<any>(null);
  const [heatmapData, setHeatmapData] = useState<any[]>([]);
  const [userActions, setUserActions] = useState<UserAction[]>([]);
  
  // 引用
  const chartRef = useRef<any>(null);
  
  // 初始化
  useEffect(() => {
    // 加载日志
    loadLogs();
    
    // 加载分析数据
    loadAnalyticsData();
    
    // 加载热图数据
    loadHeatmapData();
    
    // 加载用户行为数据
    loadUserActions();
    
    // 监听日志添加事件
    userInteractionLogService.on('logAdded', handleLogAdded);
    
    // 监听分析更新事件
    userInteractionLogService.on('analyticsUpdated', handleAnalyticsUpdated);
    
    return () => {
      // 移除事件监听
      userInteractionLogService.off('logAdded', handleLogAdded);
      userInteractionLogService.off('analyticsUpdated', handleAnalyticsUpdated);
    };
  }, []);
  
  // 过滤日志
  useEffect(() => {
    filterLogs();
  }, [logs, selectedUserId, logTypeFilter, logLevelFilter, dateRange, searchText]);
  
  /**
   * 加载日志
   */
  const loadLogs = () => {
    setIsLoading(true);
    
    try {
      const allLogs = userInteractionLogService.getLogs();
      setLogs(allLogs);
    } catch (error) {
      console.error('Failed to load logs:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  /**
   * 加载分析数据
   */
  const loadAnalyticsData = () => {
    try {
      const data = userInteractionLogService.getAnalyticsResults();
      setAnalyticsData(data);
    } catch (error) {
      console.error('Failed to load analytics data:', error);
    }
  };
  
  /**
   * 加载热图数据
   */
  const loadHeatmapData = () => {
    try {
      const data = userBehaviorAnalyzer.getHeatmapData();
      setHeatmapData(data);
    } catch (error) {
      console.error('Failed to load heatmap data:', error);
    }
  };
  
  /**
   * 加载用户行为数据
   */
  const loadUserActions = () => {
    try {
      const actions = userBehaviorAnalyzer.getActionHistory();
      setUserActions(actions);
    } catch (error) {
      console.error('Failed to load user actions:', error);
    }
  };
  
  /**
   * 处理日志添加事件
   * @param log 日志
   */
  const handleLogAdded = (log: InteractionLog) => {
    setLogs(prevLogs => [log, ...prevLogs]);
  };
  
  /**
   * 处理分析更新事件
   * @param data 分析数据
   */
  const handleAnalyticsUpdated = (data: any) => {
    setAnalyticsData(data);
  };
  
  /**
   * 过滤日志
   */
  const filterLogs = () => {
    let filtered = [...logs];
    
    // 按用户ID筛选
    if (selectedUserId) {
      filtered = filtered.filter(log => log.userId === selectedUserId);
    }
    
    // 按日志类型筛选
    if (logTypeFilter.length > 0) {
      filtered = filtered.filter(log => logTypeFilter.includes(log.type));
    }
    
    // 按日志级别筛选
    if (logLevelFilter.length > 0) {
      filtered = filtered.filter(log => logLevelFilter.includes(log.level));
    }
    
    // 按时间范围筛选
    if (dateRange) {
      filtered = filtered.filter(log => log.timestamp >= dateRange[0] && log.timestamp <= dateRange[1]);
    }
    
    // 按搜索文本筛选
    if (searchText) {
      const lowerSearchText = searchText.toLowerCase();
      filtered = filtered.filter(log => {
        // 搜索用户ID
        if (log.userId.toLowerCase().includes(lowerSearchText)) return true;
        
        // 搜索操作
        if (log.action?.toLowerCase().includes(lowerSearchText)) return true;
        
        // 搜索目标
        if (log.target?.toLowerCase().includes(lowerSearchText)) return true;
        
        // 搜索日志类型
        if (t(`interaction.logTypes.${log.type}`).toLowerCase().includes(lowerSearchText)) return true;
        
        return false;
      });
    }
    
    // 按时间倒序排序
    filtered.sort((a, b) => b.timestamp - a.timestamp);
    
    setFilteredLogs(filtered);
  };
  
  /**
   * 处理查看日志详情
   * @param log 日志
   */
  const handleViewLogDetail = (log: InteractionLog) => {
    setSelectedLog(log);
    setDetailModalVisible(true);
  };
  
  /**
   * 处理清空日志
   */
  const handleClearLogs = () => {
    confirm({
      title: t('interaction.confirmClearLogs'),
      content: t('interaction.confirmClearLogsContent'),
      onOk() {
        userInteractionLogService.clearLogs();
        setLogs([]);
      }
    });
  };
  
  /**
   * 处理切换日志记录
   * @param enabled 是否启用
   */
  const handleToggleLogging = (enabled: boolean) => {
    setLoggingEnabled(enabled);
    // 这里可以调用服务方法来启用/禁用日志记录
  };
  
  /**
   * 处理导出日志
   */
  const handleExportLogs = () => {
    try {
      const dataStr = JSON.stringify(filteredLogs, null, 2);
      const dataUri = `data:application/json;charset=utf-8,${encodeURIComponent(dataStr)}`;
      
      const exportFileDefaultName = `user_interaction_logs_${new Date().toISOString()}.json`;
      
      const linkElement = document.createElement('a');
      linkElement.setAttribute('href', dataUri);
      linkElement.setAttribute('download', exportFileDefaultName);
      linkElement.click();
    } catch (error) {
      console.error('Failed to export logs:', error);
    }
  };
  
  /**
   * 获取日志类型名称
   * @param type 日志类型
   * @returns 类型名称
   */
  const getLogTypeName = (type: InteractionLogType): string => {
    return t(`interaction.logTypes.${type}`);
  };
  
  /**
   * 获取日志级别名称
   * @param level 日志级别
   * @returns 级别名称
   */
  const getLogLevelName = (level: InteractionLogLevel): string => {
    return t(`interaction.logLevels.${level}`);
  };
  
  /**
   * 获取日志级别颜色
   * @param level 日志级别
   * @returns 颜色
   */
  const getLogLevelColor = (level: InteractionLogLevel): string => {
    switch (level) {
      case InteractionLogLevel.DEBUG:
        return 'gray';
      case InteractionLogLevel.INFO:
        return 'blue';
      case InteractionLogLevel.WARNING:
        return 'orange';
      case InteractionLogLevel.ERROR:
        return 'red';
      case InteractionLogLevel.CRITICAL:
        return 'purple';
      default:
        return 'default';
    }
  };
  
  /**
   * 格式化时间戳
   * @param timestamp 时间戳
   * @returns 格式化后的时间
   */
  const formatTimestamp = (timestamp: number): string => {
    return new Date(timestamp).toLocaleString();
  };
  
  /**
   * 表格列定义
   */
  const columns = [
    {
      title: t('interaction.timestamp'),
      dataIndex: 'timestamp',
      key: 'timestamp',
      render: (timestamp: number) => formatTimestamp(timestamp)
    },
    {
      title: t('interaction.user'),
      dataIndex: 'userName',
      key: 'userName',
      render: (userName: string, record: InteractionLog) => (
        <span>
          {userName}
          {record.userId === currentUserId && (
            <Tag color="green" style={{ marginLeft: 8 }}>
              {t('interaction.you')}
            </Tag>
          )}
        </span>
      )
    },
    {
      title: t('interaction.type'),
      dataIndex: 'type',
      key: 'type',
      render: (type: InteractionLogType) => (
        <Tag color="blue">{getLogTypeName(type)}</Tag>
      )
    },
    {
      title: t('interaction.level'),
      dataIndex: 'level',
      key: 'level',
      render: (level: InteractionLogLevel) => (
        <Tag color={getLogLevelColor(level)}>{getLogLevelName(level)}</Tag>
      )
    },
    {
      title: t('interaction.action'),
      dataIndex: 'action',
      key: 'action'
    },
    {
      title: t('interaction.target'),
      dataIndex: 'target',
      key: 'target'
    },
    {
      title: t('interaction.actions'),
      key: 'actions',
      render: (_: any, record: InteractionLog) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleViewLogDetail(record)}
          />
        </Space>
      )
    }
  ];
  
  /**
   * 渲染概览标签页
   * @returns 概览标签页内容
   */
  const renderOverviewTab = () => {
    return (
      <div className="interaction-overview-tab">
        <Row gutter={[16, 16]}>
          <Col span={6}>
            <Card>
              <Statistic
                title={t('interaction.totalLogs')}
                value={logs.length}
                prefix={<HistoryOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title={t('interaction.activeUsers')}
                value={users.filter(u => u.isActive).length}
                prefix={<TeamOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title={t('interaction.errorCount')}
                value={logs.filter(log => log.level === InteractionLogLevel.ERROR || log.level === InteractionLogLevel.CRITICAL).length}
                prefix={<DeleteOutlined />}
                valueStyle={{ color: '#cf1322' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title={t('interaction.toolUsageCount')}
                value={logs.filter(log => log.type === InteractionLogType.TOOL_USAGE).length}
                prefix={<ToolOutlined />}
              />
            </Card>
          </Col>
        </Row>
        
        <Divider />
        
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Card title={t('interaction.logsByType')}>
              {renderLogsByTypeChart()}
            </Card>
          </Col>
          <Col span={12}>
            <Card title={t('interaction.logsByLevel')}>
              {renderLogsByLevelChart()}
            </Card>
          </Col>
        </Row>
        
        <Divider />
        
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Card title={t('interaction.logsOverTime')}>
              {renderLogsOverTimeChart()}
            </Card>
          </Col>
        </Row>
      </div>
    );
  };
  
  /**
   * 渲染日志标签页
   * @returns 日志标签页内容
   */
  const renderLogsTab = () => {
    return (
      <div className="interaction-logs-tab">
        <div className="interaction-log-filters" style={{ marginBottom: 16 }}>
          <Space wrap style={{ marginBottom: 8 }}>
            <Select
              style={{ width: 200 }}
              placeholder={t('interaction.filterUser')}
              allowClear
              value={selectedUserId}
              onChange={setSelectedUserId}
            >
              {users.map(user => (
                <Option key={user.id} value={user.id}>
                  {user.name}
                </Option>
              ))}
            </Select>
            
            <Select
              style={{ width: 200 }}
              placeholder={t('interaction.filterType')}
              mode="multiple"
              allowClear
              value={logTypeFilter}
              onChange={setLogTypeFilter}
            >
              {Object.values(InteractionLogType).map(type => (
                <Option key={type} value={type}>
                  {getLogTypeName(type)}
                </Option>
              ))}
            </Select>
            
            <Select
              style={{ width: 200 }}
              placeholder={t('interaction.filterLevel')}
              mode="multiple"
              allowClear
              value={logLevelFilter}
              onChange={setLogLevelFilter}
            >
              {Object.values(InteractionLogLevel).map(level => (
                <Option key={level} value={level}>
                  {getLogLevelName(level)}
                </Option>
              ))}
            </Select>
            
            <RangePicker
              showTime
              onChange={(dates) => {
                if (dates) {
                  setDateRange([dates[0]!.valueOf(), dates[1]!.valueOf()]);
                } else {
                  setDateRange(null);
                }
              }}
            />
            
            <Input
              placeholder={t('interaction.search')}
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: 200 }}
              allowClear
            />
          </Space>
        </div>
        
        <Table
          dataSource={filteredLogs}
          columns={columns}
          rowKey="id"
          pagination={{ pageSize: 10 }}
          loading={isLoading}
          locale={{
            emptyText: <Empty description={t('interaction.noLogs')} />
          }}
        />
      </div>
    );
  };
  
  /**
   * 渲染按类型分组的日志图表
   * @returns 图表组件
   */
  const renderLogsByTypeChart = () => {
    // 统计各类型日志数量
    const typeCount: Record<string, number> = {};
    logs.forEach(log => {
      typeCount[log.type] = (typeCount[log.type] || 0) + 1;
    });
    
    const data = Object.entries(typeCount).map(([type, count]) => ({
      name: getLogTypeName(type as InteractionLogType),
      value: count
    }));

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        right: 10,
        top: 'center',
        data: data.map(item => item.name)
      },
      series: [
        {
          name: t('interaction.logsByType'),
          type: 'pie',
          radius: ['50%', '70%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '18',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data
        }
      ]
    };
    
    return <ReactECharts option={option} style={{ height: 300 }} />;
  };
  
  /**
   * 渲染按级别分组的日志图表
   * @returns 图表组件
   */
  const renderLogsByLevelChart = () => {
    // 统计各级别日志数量
    const levelCount: Record<string, number> = {};
    logs.forEach(log => {
      levelCount[log.level] = (levelCount[log.level] || 0) + 1;
    });
    
    const data = Object.entries(levelCount).map(([level, count]) => ({
      name: getLogLevelName(level as InteractionLogLevel),
      value: count
    }));

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        right: 10,
        top: 'center',
        data: data.map(item => item.name)
      },
      series: [
        {
          name: t('interaction.logsByLevel'),
          type: 'pie',
          radius: ['50%', '70%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '18',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data
        }
      ]
    };
    
    return <ReactECharts option={option} style={{ height: 300 }} />;
  };
  
  /**
   * 渲染随时间变化的日志图表
   * @returns 图表组件
   */
  const renderLogsOverTimeChart = () => {
    // 按小时统计日志数量
    const hourlyData: Record<string, number> = {};
    
    logs.forEach(log => {
      const date = new Date(log.timestamp);
      const hourKey = `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()} ${date.getHours()}:00`;
      hourlyData[hourKey] = (hourlyData[hourKey] || 0) + 1;
    });
    
    const sortedKeys = Object.keys(hourlyData).sort();
    const xAxisData = sortedKeys;
    const seriesData = sortedKeys.map(key => hourlyData[key]);
    
    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: xAxisData,
        axisLabel: {
          rotate: 45
        }
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: t('interaction.logCount'),
          type: 'line',
          data: seriesData,
          smooth: true,
          areaStyle: {}
        }
      ]
    };
    
    return <ReactECharts option={option} style={{ height: 300 }} />;
  };
  
  return (
    <div className={`user-interaction-analysis-panel ${className || ''}`}>
      <Card
        title={
          <Space>
            <BarChartOutlined />
            {t('interaction.title')}
          </Space>
        }
        extra={
          <Space>
            <Select
              placeholder={t('interaction.enableLogging')}
              value={loggingEnabled}
              onChange={handleToggleLogging}
              style={{ width: 120 }}
            >
              <Option value={true}>{t('enabled')}</Option>
              <Option value={false}>{t('disabled')}</Option>
            </Select>
            <Button
              icon={<ReloadOutlined />}
              onClick={loadLogs}
            >
              {t('interaction.refresh')}
            </Button>
            <Button
              icon={<DownloadOutlined />}
              onClick={handleExportLogs}
            >
              {t('interaction.export')}
            </Button>
            <Popconfirm
              title={t('interaction.confirmClearLogs')}
              onConfirm={handleClearLogs}
              okText={t('yes')}
              cancelText={t('no')}
            >
              <Button 
                icon={<DeleteOutlined />} 
                danger
              >
                {t('interaction.clearLogs')}
              </Button>
            </Popconfirm>
          </Space>
        }
      >
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
        >
          <TabPane
            tab={
              <span>
                <DashboardOutlined />
                {t('interaction.overview')}
              </span>
            }
            key="overview"
          >
            {renderOverviewTab()}
          </TabPane>
          
          <TabPane
            tab={
              <span>
                <HistoryOutlined />
                {t('interaction.logs')}
              </span>
            }
            key="logs"
          >
            {renderLogsTab()}
          </TabPane>
          
          <TabPane
            tab={
              <span>
                <HeatMapOutlined />
                {t('interaction.heatmap')}
              </span>
            }
            key="heatmap"
          >
            {/* 热图标签页内容 */}
          </TabPane>
          
          <TabPane
            tab={
              <span>
                <UserOutlined />
                {t('interaction.userBehavior')}
              </span>
            }
            key="userBehavior"
          >
            {/* 用户行为标签页内容 */}
          </TabPane>
          
          <TabPane
            tab={
              <span>
                <SettingOutlined />
                {t('interaction.settings')}
              </span>
            }
            key="settings"
          >
            {/* 设置标签页内容 */}
          </TabPane>
        </Tabs>
      </Card>
      
      <Modal
        title={t('interaction.logDetail')}
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            {t('close')}
          </Button>
        ]}
        width={800}
      >
        {selectedLog && (
          <div>
            <Descriptions bordered column={2}>
              <Descriptions.Item label={t('interaction.id')}>
                {selectedLog.id}
              </Descriptions.Item>
              <Descriptions.Item label={t('interaction.timestamp')}>
                {formatTimestamp(selectedLog.timestamp)}
              </Descriptions.Item>
              <Descriptions.Item label={t('interaction.user')}>
                {selectedLog.userName}
              </Descriptions.Item>
              <Descriptions.Item label={t('interaction.userId')}>
                {selectedLog.userId}
              </Descriptions.Item>
              <Descriptions.Item label={t('interaction.type')}>
                <Tag color="blue">{getLogTypeName(selectedLog.type)}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label={t('interaction.level')}>
                <Tag color={getLogLevelColor(selectedLog.level)}>
                  {getLogLevelName(selectedLog.level)}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label={t('interaction.action')}>
                {selectedLog.action || '-'}
              </Descriptions.Item>
              <Descriptions.Item label={t('interaction.target')}>
                {selectedLog.target || '-'}
              </Descriptions.Item>
              <Descriptions.Item label={t('interaction.targetType')}>
                {selectedLog.targetType || '-'}
              </Descriptions.Item>
              <Descriptions.Item label={t('interaction.sessionId')}>
                {selectedLog.sessionId || '-'}
              </Descriptions.Item>
              <Descriptions.Item label={t('interaction.duration')} span={2}>
                {selectedLog.duration ? `${selectedLog.duration} ms` : '-'}
              </Descriptions.Item>
              <Descriptions.Item label={t('interaction.details')} span={2}>
                <pre style={{ maxHeight: 300, overflow: 'auto' }}>
                  {JSON.stringify(selectedLog.details, null, 2) || '-'}
                </pre>
              </Descriptions.Item>
            </Descriptions>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default UserInteractionAnalysisPanel;
